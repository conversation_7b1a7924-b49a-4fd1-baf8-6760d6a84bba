<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViralDeals Price Calculator</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .calculator-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            transform: scale(1.2);
        }

        .calculate-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .results-section {
            background: #fff;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .result-card {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            display: none;
        }

        .result-card.show {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 15px;
        }

        .price-breakdown {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .breakdown-item {
            background: rgba(255,255,255,0.7);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .breakdown-item .label {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .breakdown-item .value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .tier-info {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .bulk-section {
            grid-column: 1 / -1;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            margin-top: 20px;
        }

        .bulk-input {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: monospace;
            font-size: 0.9rem;
            resize: vertical;
        }

        .bulk-results {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .bulk-result-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            align-items: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .price-breakdown {
                grid-template-columns: 1fr;
            }
            
            .bulk-result-item {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> ViralDeals Price Calculator</h1>
            <p>Intelligent markup prediction for maximum profitability</p>
        </div>

        <div class="main-content">
            <!-- Single Product Calculator -->
            <div class="calculator-section">
                <h2 class="section-title">
                    <i class="fas fa-tag"></i> Single Product Pricing
                </h2>

                <div class="form-group">
                    <label for="supplierPrice">Supplier Price (₹)</label>
                    <input type="number" id="supplierPrice" placeholder="Enter supplier price" min="1" step="0.01">
                </div>

                <div class="form-group">
                    <label for="category">Product Category</label>
                    <select id="category">
                        <option value="generic">Generic</option>
                        <option value="electronics">Electronics</option>
                        <option value="fashion">Fashion</option>
                        <option value="home_kitchen">Home & Kitchen</option>
                        <option value="beauty">Beauty</option>
                        <option value="sports">Sports</option>
                        <option value="books">Books</option>
                        <option value="toys">Toys</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="competition">Competition Level</label>
                    <select id="competition">
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                        <option value="high">High</option>
                        <option value="very_high">Very High</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="uniqueValue">
                    <label for="uniqueValue">Has unique value proposition</label>
                    <i class="fas fa-info-circle tooltip" data-tooltip="Check if you offer bundles, warranty, fast shipping, etc."></i>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="psychological" checked>
                    <label for="psychological">Apply psychological pricing</label>
                    <i class="fas fa-info-circle tooltip" data-tooltip="Prices ending in 9s (₹199, ₹999, etc.)"></i>
                </div>

                <button class="calculate-btn" onclick="calculatePrice()">
                    <i class="fas fa-calculator"></i> Calculate Price
                </button>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i> Pricing Results
                </h2>

                <div id="results" class="result-card">
                    <div class="price-display" id="finalPrice">₹0</div>
                    
                    <div class="price-breakdown">
                        <div class="breakdown-item">
                            <div class="label">Base Markup</div>
                            <div class="value" id="baseMarkup">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="label">Adjusted Markup</div>
                            <div class="value" id="adjustedMarkup">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="label">Profit Margin</div>
                            <div class="value" id="profitMargin">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="label">Total Costs</div>
                            <div class="value" id="totalCosts">₹0</div>
                        </div>
                    </div>
                </div>

                <div class="tier-info">
                    <h4><i class="fas fa-layer-group"></i> Markup Tiers</h4>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li>₹100-₹299: <strong>60%</strong></li>
                        <li>₹300-₹699: <strong>45%</strong></li>
                        <li>₹700-₹1,199: <strong>35%</strong></li>
                        <li>₹1,200-₹2,000: <strong>25%</strong></li>
                        <li>₹2,000+: <strong>20%</strong></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bulk Calculator -->
        <div class="bulk-section">
            <h2 class="section-title">
                <i class="fas fa-list"></i> Bulk Price Calculator
            </h2>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label for="bulkInput">Product List (JSON format)</label>
                    <textarea id="bulkInput" class="bulk-input" placeholder='[
  {"supplier_price": 150, "category": "electronics", "competition": "high"},
  {"supplier_price": 500, "category": "fashion", "competition": "medium"},
  {"supplier_price": 900, "category": "beauty", "competition": "low"}
]'></textarea>
                    <button class="calculate-btn" onclick="calculateBulk()">
                        <i class="fas fa-calculator"></i> Calculate Bulk Prices
                    </button>
                </div>
                
                <div id="bulkResults" class="bulk-results"></div>
            </div>
        </div>
    </div>

    <script src="pricing_calculator.js"></script>
</body>
</html>
